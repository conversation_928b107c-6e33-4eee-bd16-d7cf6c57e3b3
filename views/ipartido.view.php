<?php
#region region DOCS
/** @var Partido $newpartido */
/** @var ApuestaTipo[] $apuestastipos */
/** @var Pais[] $paises */
/** @var PartidoInfo[] $teams */
/** @var int $count_partidos_mismotorneo_porrevisar */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Partidos</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <!-- BEGIN HEAD -->
    <?php require_once __ROOT__ . '/views/head.view.php';?>	
    <link href="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
    <link id="region_CSS_slider" href="<?php echo RUTA ?>resources/assets/plugins/ion-rangeslider/css/ion.rangeSlider.min.css" rel="stylesheet" />
    <!-- END HEAD -->
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <h4>
            Ingresar partido
            <span class="badge bg-primary rounded-0 fs-11px">
                <?php echo $count_partidosporrevisar; ?> por revisar
            </span>
        </h4>

        <hr>
        <!-- END page-header -->

        <?php #region FORM ?>
        <form action="ipartido" method="POST">
            <input type="hidden" id="idpartidoporrevisar" name="idpartidoporrevisar" value="<?php echo @recover_var($idpartidoporrevisar) ?>">
            <input type="hidden" id="formhome" name="formhome" value="<?php echo @recover_var($newpartido->formhome) ?>">
            <input type="hidden" id="formaway" name="formaway" value="<?php echo @recover_var($newpartido->formaway) ?>">
            <input type="hidden" id="home_xg" name="home_xg" value="<?php echo @recover_var($newpartido->home_xg) ?>">
            <input type="hidden" id="away_xg" name="away_xg" value="<?php echo @recover_var($newpartido->away_xg) ?>">
                 
            <!-- ROW -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <label for="home" class="d-flex align-items-center fs-12px">
                        Home:
                    </label>
                    <input type="text" class="form-control form-control-fh fs-12px no-border-radious" name="home" id="home" value="<?php echo @recover_var($newpartido->home) ?>" autofocus onclick="copy_home()" />
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <label for="away" class="d-flex align-items-center fs-12px">
                        Away:
                    </label>
                    <input type="text" class="form-control form-control-fh fs-12px no-border-radious" name="away" id="away" value="<?php echo @recover_var($newpartido->away) ?>" onclick="copy_away()" />
                </div>
                <!-- END text -->
            </div>
            <!-- END ROW -->
            <!-- ROW -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <label for="pais" class="d-flex align-items-center fs-12px">
                        Pais:
                    </label>
                    <div class="input-group">
                        <input type="text" name="pais" id="pais" class="form-control form-control-fh fs-12px no-border-radious" value="<?php echo @recover_var($newpartido->pais) ?>" onclick="copy_pais()" />
                        <?php if($paiscreado == 1) : ?>
                            <span class="input-group-text no-border-radious  bg-success <?php echo $fontsizeicons; ?>" data-toggle="tooltip" data-placement="top" title="Pais creado">
                                <i class="fa fa-check fa-md cursor-pointer"></i>                                
                            </span>
                        <?php else : ?>
                            <span class="input-group-text no-border-radious bg-danger <?php echo $fontsizeicons; ?>" data-toggle="tooltip" data-placement="top" title="Pais no creado">
                                <i class="fa fa-xmark fa-md cursor-pointer"></i>
                            </span>
                        <?php endif; ?>
                        <?php if($infocargada == 1) : ?>
                            <span class="input-group-text no-border-radious bg-success <?php echo $fontsizeicons; ?>" data-toggle="tooltip" data-placement="top" title="Info. cargada">
                                <i class="fa fa-check fa-md cursor-pointer"></i>                                
                            </span>
                        <?php else : ?>
                            <span class="input-group-text no-border-radious bg-danger <?php echo $fontsizeicons; ?>" data-toggle="tooltip" data-placement="top" title="Info. no cargada">
                                <i class="fa fa-xmark fa-md cursor-pointer"></i>
                            </span>
                        <?php endif; ?>
                        <span class="input-group-text no-border-radious bg-primary fs-10px">
                            <?php echo $count_partidos_mismotorneo_porrevisar; ?>
                        </span>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN date -->
                <div class="col-md-3 col-xs-12">
                    <label for="fecha" class="d-flex align-items-center fs-12px">
                        Fecha:
                    </label>
                    <input type="text" class="form-control form-control-fh fs-12px no-border-radious datepicker" id="fecha" name="fecha" value="<?php echo @recover_var($newpartido->fecha) ?>" autocomplete="off"/>
                </div>
                <!-- END date -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <label for="horamilitar" class="d-flex align-items-center fs-12px">
                        hora:
                    </label>
                    <input type="text" class="form-control text-center form-control-fh fs-12px no-border-radious" name="horamilitar" id="horamilitar" value="<?php echo @recover_var($horamilitar) ?>" readonly />
                </div>
                <!-- END text -->
            </div>
            <!-- END ROW -->
            <!-- ROW -->
            <div class="row mt-3">
                <!-- BEGIN slider -->
                <div class="col-md-12 col-xs-12">
                    <label for="hora" class="fs-15px">
                        Hora:
                    </label>
                    <input type="text" id="hora" name="hora" value="<?php echo @recover_var($newpartido->horamilitar) ?>" />
                    <!-- Countdown note directly below the hora field -->
                    <div id="countdown_note" class="alert alert-secondary mt-2 py-2 px-2 d-none" role="alert">
                        <i class="fa fa-clock me-1"></i>
                        <span id="countdown_text" class="small">Calculando tiempo restante...</span>
                    </div>
                </div>
                <!-- END slider -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <?php #region region SUBMIT sub_add_next ?>
                <div class="col-md-12 col-xs-12">
                    <button type="submit" id="sub_add_next" name="sub_add_next" class="btn btn-md btn-success w-100">
                        Agregar y ver siguiente
                    </button>
                </div>
                <?php #endregion SUBMIT sub_add_next ?>
            </div>
            <!-- END ROW -->
            <!-- ROW -->
            <div class="row mt-3">
                <!-- LINK -->
                <div class="col-md-4 col-xs-12">
                    <a href="#mdl_ocultarpais" data-bs-toggle="modal" class="region_LINK_ocultarpais btn btn-xs btn-danger w-100">
                        Ocultar torneo
                    </a>
                </div>
                <!-- END LINK -->
                <?php #region region LINK eliminar ?>
                <div class="col-md-4 col-xs-12">
                    <a href="#mdl_delpartidoporrevisar" data-bs-toggle="modal" class="region_LINK_delpartidoporrevisar btn btn-xs btn-danger w-100">
                        Eliminar
                    </a>
                </div>
                <?php #endregion LINK eliminar ?>
                <?php #region region LINK eliminar ?>
                <div class="col-md-4 col-xs-12">
                    <a href="#mdl_del_all_partidos_torneo" data-bs-toggle="modal" class="btn btn-xs btn-danger w-100">
                        Eliminar todos del torneo
                    </a>
                </div>
                <?php #endregion LINK eliminar ?>
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN link -->
                <div class="col-md-6 col-xs-12">
                    <a href="lpartidosporrevisar" class="btn btn-xs btn-default w-100">
                        Ir a por revisar
                    </a>
                </div>
                <!-- END link -->
                <!-- BEGIN link -->
                <div class="col-md-6 col-xs-12">
                    <a href="lpartidos" class="btn btn-xs btn-default w-100">
                        Ir a partidos
                    </a>
                </div>
                <!-- END link -->
            </div>
            <!-- END ROW -->
            <?php #region region MODAL mdl_delpartidoporrevisar ?>
            <div class="modal fade" id="mdl_delpartidoporrevisar">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Eliminar partido</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <p>Esta seguro que desea eliminar este partido por revisar?</p>
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                Cancelar
                            </a>
                            <button type="submit" id="sub_delpartidoporrevisar" name="sub_delpartidoporrevisar" class="btn btn-danger no-border-radious">
                                Eliminar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion MODAL mdl_delpartidoporrevisar ?>
            <?php #region region MODAL mdl_del_all_partidos_torneo ?>
            <div class="modal fade" id="mdl_del_all_partidos_torneo">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Eliminar todos los partidos del torneo</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <p>Esta seguro que desea eliminar todos los partidos del torneo?</p>
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                Cancelar
                            </a>
                            <button type="submit" id="sub_del_all_partidos_torneo" name="sub_del_all_partidos_torneo" class="btn btn-danger no-border-radious">
                                Eliminar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion MODAL mdl_del_all_partidos_torneo ?>
            <!-- BEGIN mdl_ocultarpais -->
            <div class="region_MODAL_mdl_ocultarpais modal fade" id="mdl_ocultarpais">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Ocultar torneo</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <p>Esta seguro que desea ocultar este torneo?</p>
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                Cancelar
                            </a>
                            <button type="submit" id="sub_ocultarpais" name="sub_ocultarpais" class="btn btn-danger no-border-radious">
                                Ocultar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END mdl_ocultarpais -->
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script id="region_JS" src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<!-- BEGIN JS date -->
<script id="region_JS_date" src="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA ?>resources/js/datepickerini.js"></script>
<!-- END JS date -->
<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $success_text; ?>",
            icon: 'success',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-success',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $error_text; ?>",
            icon: 'error',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>
<?php #region region JS ion slider ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/ion-rangeslider/js/ion.rangeSlider.min.js"></script>
<script>
    $("#hora").ionRangeSlider({
        min: 0,
        max: 23,
        maxPostfix: "+",
        prettify: false,
        hasGrid: true,
        skin: "big"
    });
</script>
<?php #endregion JS ion slider ?>
<!-- BEGIN JS countdown (America/Bogota) -->
<script id="region_JS_countdown" type="text/javascript">
(function(){
  // Bogotá timezone is UTC-5 (no DST). We'll convert Bogotá local time to UTC by adding 5 hours.
  var BOGOTA_OFFSET_MIN = -5 * 60; // minutes relative to UTC

  function getBogotaStartUtcMs(){
    var fechaEl = document.getElementById('fecha');
    var horaMilEl = document.getElementById('horamilitar');
    var horaSliderEl = document.getElementById('hora');

    var fecha = (fechaEl && fechaEl.value ? fechaEl.value.trim() : '');
    var horaMilStr = (horaMilEl && horaMilEl.value ? horaMilEl.value.trim() : '');

    var hour = null, minute = 0;
    if (horaMilStr) {
      var m = horaMilStr.match(/^([0-2]?\d)(?::([0-5]\d))?/);
      if (m) {
        hour = parseInt(m[1], 10);
        if (!isNaN(hour) && m[2]) minute = parseInt(m[2], 10);
      }
    }
    if (hour === null && horaSliderEl && horaSliderEl.value !== '') {
      var hs = parseInt(horaSliderEl.value, 10);
      if (!isNaN(hs)) hour = hs;
    }

    if (!fecha || hour === null || isNaN(hour)) return null;
    var parts = fecha.split('-');
    if (parts.length < 3) return null;
    var y = parseInt(parts[0],10), mo = parseInt(parts[1],10), d = parseInt(parts[2],10);
    if (!y || !mo || !d) return null;

    // Convert Bogotá local time to UTC by adding 5 hours (i.e., subtracting negative offset)
    var utcMs = Date.UTC(y, mo - 1, d, hour - (BOGOTA_OFFSET_MIN/60), minute, 0, 0);
    return utcMs;
  }

  function formatDuration(ms){
    var totalSec = Math.max(0, Math.floor(ms / 1000));
    var days = Math.floor(totalSec / 86400);
    var hours = Math.floor((totalSec % 86400) / 3600);
    var minutes = Math.floor((totalSec % 3600) / 60);
    if (days > 0) return days + 'd ' + hours + 'h ' + minutes + 'm restantes';
    if (hours > 0) return hours + ' horas ' + minutes + ' minutos restantes';
    return minutes + ' minutos restantes';
  }

  function updateCountdown(){
    var note = document.getElementById('countdown_note');
    var text = document.getElementById('countdown_text');
    if (!note || !text) return;

    var startUtc = getBogotaStartUtcMs();
    if (startUtc === null) {
      text.textContent = 'Fecha y hora no definidas';
      note.className = 'alert alert-secondary mt-2 py-2 px-2';
      note.classList.remove('d-none');
      return;
    }

    var nowUtc = Date.now();
    var matchDurationMs = 2.5 * 60 * 60 * 1000; // approx 2.5 hours

    if (nowUtc < startUtc) {
      var diff = startUtc - nowUtc;
      text.textContent = 'Inicia en ' + formatDuration(diff);
      note.className = 'alert alert-info mt-2 py-2 px-2';
      note.classList.remove('d-none');
    } else if (nowUtc >= startUtc && nowUtc <= startUtc + matchDurationMs) {
      text.textContent = 'Partido en progreso';
      note.className = 'alert alert-warning mt-2 py-2 px-2';
      note.classList.remove('d-none');
    } else {
      text.textContent = 'Partido finalizado';
      note.className = 'alert alert-dark mt-2 py-2 px-2';
      note.classList.remove('d-none');
    }
  }

  document.addEventListener('DOMContentLoaded', function(){
    // Initial update
    updateCountdown();

    // React to user changes
    var fechaEl = document.getElementById('fecha');
    var horaSliderEl = document.getElementById('hora');
    var horaMilEl = document.getElementById('horamilitar');

    if (fechaEl) fechaEl.addEventListener('change', updateCountdown);
    if (horaSliderEl) {
      horaSliderEl.addEventListener('change', updateCountdown);
      horaSliderEl.addEventListener('input', updateCountdown);
    }
    if (horaMilEl) horaMilEl.addEventListener('change', updateCountdown);

    // Real-time updates
    setInterval(updateCountdown, 1000);
  });
})();
</script>
<!-- END JS countdown (America/Bogota) -->
<!-- BEGIN JS autocomplete -->
<script id="region_JS_autocomplete_pais" type="text/javascript">
  $("#pais").autocomplete({
    source: [
        <?php foreach ($paises as $pais): ?>
            "<?php echo $pais->nombre; ?>",	 
        <?php endforeach; ?>		
    ]
  });
</script>
<!-- BEGIN JS autocomplete -->
<!-- BEGIN JS autocomplete -->
<script id="region_JS_autocomplete_home" type="text/javascript">
  $("#home").autocomplete({
    source: [
        <?php foreach ($teams as $team): ?>
            "<?php echo $team->team; ?>",	 
        <?php endforeach; ?>		
    ]
  });
</script>
<!-- BEGIN JS autocomplete -->
<!-- BEGIN JS autocomplete -->
<script id="region_JS_autocomplete_away" type="text/javascript">
  $("#away").autocomplete({
    source: [
        <?php foreach ($teams as $team): ?>
            "<?php echo $team->team; ?>",	 
        <?php endforeach; ?>		
    ]
  });
</script>
<!-- BEGIN JS autocomplete -->
<!--BEGIN tooltip -->
<script type="text/javascript">
    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })
</script>
<!--END tooltip -->
<?php #region region JS copy home ?>
<script type="text/javascript">
    function copy_home() {
        // Get the text field
        var copyText = document.getElementById("home");

        // Select the text field
        copyText.select();
        copyText.setSelectionRange(0, 99999); // For mobile devices

        // Copy the text inside the text field
        //navigator.clipboard.writeText(copyText.value);
        navigator.clipboard.writeText(copyText.value.substring(0, 13));

        // Alert the copied text
        //alert("Copied the text: " + copyText.value);
    }
</script>
<?php #endregion JS copy home ?>
<?php #region region JS copy away ?>
<script type="text/javascript">
    function copy_away() {
        // Get the text field
        var copyText = document.getElementById("away");

        // Select the text field
        copyText.select();
        copyText.setSelectionRange(0, 99999); // For mobile devices

        // Copy the text inside the text field
        //navigator.clipboard.writeText(copyText.value);
        navigator.clipboard.writeText(copyText.value.substring(0, 13));

        // Alert the copied text
        //alert("Copied the text: " + copyText.value);
    }
</script>
<?php #endregion JS copy away ?>
<?php #region region JS copy pais ?>
<script type="text/javascript">
    function copy_pais() {
        // Get the text field
        var copyText = document.getElementById("pais");

        // Select the text field
        copyText.select();
        copyText.setSelectionRange(0, 99999); // For mobile devices

        // Copy the text inside the text field
        navigator.clipboard.writeText(copyText.value);

        // Alert the copied text
        //alert("Copied the text: " + copyText.value);
    }
</script>
<?php #endregion JS copy pais ?>
<?php #region region JS onload copy home ?>
<script type="text/javascript">
    window.onload = copy_home();
</script>
<?php #endregion JS onload copy home ?>
<?php #endregion JS ?>

</body>
</html>