<?php // ## doc
/** @var array $parahoytasks */
/** @var ParaHoy $parahoytask */
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | For today</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <!-- #head -->
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/sidebar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <!-- BEGIN page-header -->
        <h1 class="page-header">For today</h1>
        <!-- END page-header -->

        <!-- ## BEGIN form -->
        <form action="lparahoy" method="POST">
            <!-- BEGIN row -->
            <div class="row ">
                <div class="col ">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="description" id="description" placeholder="Description:" autofocus/>
                        <label for="description" class="d-flex align-items-center fs-15px">
                            Description:
                        </label>
                    </div>
                </div>
                <div class="col-2">
                    <!-- sub_add agregar registro -->
                    <button type="submit" id="sub_add" name="sub_add" class="btn btn-success btn-inline w-100">
                        <i class="fa fa-plus fa-lg fa-fw"></i>
                    </button>
                </div>
            </div>
            <!-- END row -->
            <!-- BEGIN panel -->
            <div class="panel panel-inverse mt-3">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        For today:
                    </h4>
                </div>
                <!-- BEGIN panel-body -->
                <div class="panel-body table-nowrap">
                    <!-- BEGIN table parahoytasks -->
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="w-10px"></th>
                            <th class="w-10px"></th>
                            <th>Description</th>
                            <th class="text-center">Done</th>
                        </tr>
                        </thead>
                        <tbody class="fs-15px">
                        <!-- ## BEGIN array parahoytasks -->
                        <?php foreach ($parahoytasks as $parahoytask): ?>
                            <tr>
                                <td>
                                    <a href="#mdl_donetask" data-bs-toggle="modal" data-id_parahoy="<?php echo limpiar_datos($parahoytask->id_parahoy) ?>" class="btn btn-success btn-xs w-100">
                                        <i class="fa fa-check fa-lg fa-fw"></i>
                                    </a>
                                </td>
                                <td>
                                    <a class="btn btn-danger btn-xs w-100" href="#mdl_deltask" data-bs-toggle="modal" data-id_parahoy="<?php echo limpiar_datos($parahoytask->id_parahoy) ?>">
                                        <i class="fa fa-times fa-lg fa-fw"></i>
                                    </a>
                                </td>
                                <?php if ($parahoytask->isdone == 1): ?>
                                    <td class="text-success"><?php echo $parahoytask->description; ?></td>
                                    <td class="text-center"><i class="text-success fa fa-circle-check fa-lg fa-fw"></i></td>
                                <?php else: ?>
                                    <td><?php echo $parahoytask->description; ?></td>
                                    <td class="text-center"><i class="text-danger fa fa-circle-xmark fa-lg fa-fw"></i></td>
                                <?php endif; ?>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <!-- END table -->
                </div>
                <!-- END panel-body -->
            </div>
            <!-- END panel -->
            <!-- ## BEGIN mdl_donetask -->
            <div class="modal fade" id="mdl_donetask">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <input type="hidden" id="mdldonetask_id_parahoy" name="mdldonetask_id_parahoy">

                        <div class="modal-header">
                            <h4 class="modal-title">Task done</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <p>Are you sure this task is done?</p>
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                <i class="fa fa-arrow-left fa-lg fa-fw"></i>
                            </a>
                            <button type="submit" id="sub_donetask" name="sub_donetask" class="btn btn-success">
                                <i class="fa fa-check fa-lg fa-fw"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END mdl_donetask -->
            <!-- ## BEGIN mdl_deltask -->
            <div class="modal fade" id="mdl_deltask">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <input type="hidden" id="mdldeltask_id_parahoy" name="mdldeltask_id_parahoy">

                        <div class="modal-header">
                            <h4 class="modal-title">Delete task</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <p>Are you sure you want to delete this task?</p>
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                <i class="fa fa-arrow-left fa-lg fa-fw"></i>
                            </a>
                            <button type="submit" id="sub_deltask" name="sub_deltask" class="btn btn-danger">
                                <i class="fa fa-check fa-lg fa-fw"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END mdl_deltask -->
        </form>
        <!-- END form -->
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<!-- BEGIN js -->
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->

<script type="text/javascript">
    pressenterandclick('description', 'sub_add');
</script>
<!-- ## mdl_donetask script -->
<script type="text/javascript">
    $('#mdl_donetask').on('shown.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var recipient_id_parahoy = button.data('id_parahoy');

        var mdldonetask_id_parahoy = document.getElementById('mdldonetask_id_parahoy');

        mdldonetask_id_parahoy.value = recipient_id_parahoy;
    })
</script>
<!-- modal script -->
<!-- ## mdl_deltask script -->
<script type="text/javascript">
    $('#mdl_deltask').on('shown.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var recipient_id_parahoy = button.data('id_parahoy');

        var mdldeltask_id_parahoy = document.getElementById('mdldeltask_id_parahoy');

        mdldeltask_id_parahoy.value = recipient_id_parahoy;
    })
</script>
<!-- modal script -->
</body>
</html>